syntax = "proto3";

// 指定Java文件包路径
option java_package="cn.taken.ad.logic.adv.tianzao.dto";
// 是否拆分为多个Java类
option java_multiple_files = true;


//option java_outer_classname = "TianZaoBidRequest";


// 请求体
message BidRequest {
  // 媒体请求时的唯一ID(需媒体自定义)
  string id = 1;
  // 本API版本号，例如：1.0.0
  string ver = 2;
  // 广告位信息
  Imp imp = 3;
  // APP信息
  App app = 4;
  // 用户信息
  optional User user = 5;
  // 设备信息

  Device device = 6;
  message Imp {
    //广告位 id（联系平台商务人员获取）
    string tagId = 1;
    //广告位宽
    sint32 w = 2;
    //广告位高
    sint32 h = 3;
    //广告类型，1：横幅；2：信息流；3：开屏； 4：插屏；5：视频；6：激励视频；
    sint32 type = 4;
    //广告展现位置，1：顶部；2：底部；3：信息流内；4：中部；5：全屏；
    sint32 pos = 5;
    //可接受的创意类型，1：文字；2：静态图片；3：图文，4：图文+文字，5：视频；
    repeated sint32 cType = 6;
    //支持的创意交互类型，1：使用浏览器打开；2：应用内打开；3：下载应用；
    repeated sint32 ciType = 7;
    //是否支持DeepLink，1：不支持；2：支持，默认支持DP；
    optional sint32 dp = 8;
    //媒体底价，单位: 分/cpm，竞价模式必传
    optional int32 bidFloor = 9;
  }
  message App {
    //APP名称
    string name = 1;
    //APP包名
    string bundle = 2;
    //APP版本号
    string ver = 3;
    //APP应用商店下载地址
    optional string storeUrl = 4;
  }
  message User {
    //用户ID
    optional string id = 1;
    //用户生日年份，四位数字表示，例如：1991
    optional string yob = 2;
    //用户 性别，0：未知，1：男性，2：女性；
    optional sint32 gender = 3;
    // 用户画像关键词列表，以逗号分隔
    optional string keywords = 4;
  }
  message Device {
    //设备IP
    string ip = 1;
    //ipv6
    optional string ipv6 = 2;
    //设备User-Agent
    string ua = 3;
    // 设备OS类型，取值：IOS、Android
    string os = 4;
    //设备系统版本号，需与UA中的系统版本号一致，例如：8.0.0
    string osv = 5;
    //设备类型，0：未知，1：手机，2：平板，3：智能电视；
    sint32 deviceType = 6;
    //设备经纬度信息
    Geo geo = 7;
    //设备网络信息
    Network network = 8;
    //设备厂商，例如：Apple、HUAWEI
    string brand = 9;
    //设备型号，IOS：Hardware(硬件字符串，例如：iPhone11,8)；
    // (IOS 十参数必传)
    string model = 10;
    //设备型号码，IOS必传(例如：D22AP);    //(IOS 十参数必传)
    string modelCode = 11;
    //设备屏幕方向，1：竖屏，2：横屏；
    sint32 orientation = 12;
    //设备屏幕(分辨率)宽，例如：1080
    sint32 dw = 13;
    //设备屏幕(分辨率)高，例如：1920
    sint32 dh = 14;
    //设备屏幕像素密度，例如：2.5
    double density = 15;
    //设备屏幕每英寸像素点数，例如：460
    sint32 ppi = 16;
    //设备屏幕尺寸，例如：4.7、5.5英寸
    double screenSize = 17;
    //设备序列号 Build.SERIAL
    optional string serialno = 18;
    //Android 设备 ID，Android必传
    optional string anId = 19;
    // Android 设备 ID MD5值
    optional string anIdMd5 = 20;
    //Android 设备 imei，Android 10 以下必传
    optional string imei = 21;
    //Android 设备 imei MD5值
    optional string imeiMd5 = 22;
    //ndroid 设备 oaid，Android 10 以上必传
    optional string oaid = 23;
    //Android 设备 oaid MD5值
    optional string oaidMd5 = 24;
    //droid 设备系统版本，IOS 设备无需此参数
    optional string apiLevel = 25;
    //拼多多PAID，PAID = MD5(设备初始化时间) + "-" + MD5(系统更新时间) + "-" + MD5(系统启动时间)
    optional string paid = 26;
    //IOS 设备 idfa，IOS 14 以下必传
    optional string idfa = 27;
    //IOS 设备 idfa MD5值

    optional string idfaMd5 = 28;
    //IOS 设备 caid，IOS 14 以上必传

    optional string caid = 29;
    //IOS 设备 caid MD5值
    optional string caidMd5 = 30;
    // IOS 设备 caid 版本号，IOS 14 以上必传
    optional string caidVer = 31;
    //IOS 设备 idfv，IOS必传
    optional string idfv = 32;
    //IOS 设备 idfv MD5值
    optional string idfvMd5 = 33;
    //IOS 设备 软件生成替代 UDID 的标识
    optional string openUdid = 34;
    //设备名称(IOS必传，IOS无法获取时，传deviceNameMd5)；
    // (IOS 十参数必传)
    optional string deviceName = 35;
    //设备名称 MD5值
    optional string deviceNameMd5 = 36;
    //设备语言，IOS必传，例如：zh-Hans-CN； //(IOS 十参数必传)
    optional string language = 37;
    //设备国家，IOS必传，例如：CN；
    // (IOS 十参数必传)
    optional string country = 38;
    //设备ROM版本
    optional string romVer = 39;
    //系统编译时间
    optional string sysComplingTime = 40;
    //设备开机时间，秒级时间戳；    //(IOS 十参数必传)
    optional sint32 bootTime = 41;
    //系统更新时间，秒级时间戳；    //(IOS 十参数必传)
    optional sint32 updateTime = 42;
    //设备初始化时间，例如：1649783466.444164583
    optional string initTime = 43;
    //设备磁盘大小，单位：GB，例如：128；
    // (IOS 十参数必传)
    optional sint32 diskSize = 44;
    //设备内存大小，单位：GB，例如：4；    //(IOS 十参数必传)
    optional sint32 memorySize = 45;
    //电池充电状态，0：未知，1：不充电，2：充电，3：满电；
    optional sint32 batteryStatus = 46;
    //电池电量百分比，例如：60
    optional sint32 batteryPower = 47;
    //设备CPU个数，例如：4
    optional sint32 cpuNum = 48;
    //设备CPU频率，单位:GHz，例如：2.2
    optional double cpuFre = 49;
    //设备所处时区，例如：28800；    //(IOS 十参数必传)
    optional string timeZone = 50;
    //设备是否允许获取IDFA，0：未确定，1：受限制，2：被拒绝，3：授权；
    optional sint32 lmt = 51;
    //设备定位精准度，0：定位精准，可以获取到小数点4位及以上，1：定位不准确；
    optional sint32 laccu = 52;
    //系统启动标识，例如：    //IOS：1623815045.970028，    //Android：ec7f4f33-411a-47bc-8067-744a4e7e0723；
    string bootMark = 53;
    //系统更新标识，例如：    //iOS：1581141691.570419583，    //Android：1004697.709999999；
    string updateMark = 54;
    //设备应用商店版本，VIVO、OPPO、HUAWEI必传；
    optional string appStoreVer = 55;
    //HUAWEI 设备的 HMSCore 版本号
    optional string hmsVer = 56;
    //skan 版本，IOS 14 以上必传
    repeated string skadnetworkVer = 57;
    //设备已安装的APP包名列表
    repeated string installedApp = 58;
    //深度因子 t2(IOS 选填)
    optional string t2 = 59;
    //深度因子 t8(IOS 选填)
    optional string t8 = 60;
    //keychain 持久化 id(IOS 选填)
    optional string kid = 61;
    // CAID供应商, 0:热云, 1:信通院, 2:阿里因子AAID
    optional sint32 caidVendor = 62;
    // 设备开机时间，精确到纳秒， 格式：1649125566.567166753，纳秒为9位。    //(IOS 十参数必传)
    optional string bootTimeNano = 63;
    //系统更新时间，精确到纳秒， 格式：1649125566.567166753，纳秒为9位。
    optional string updateTimeNano = 64;
    // IOS caid List，14以上必传，新老版本都要传；
    repeated CaidList caidList = 65;
  }
  message Geo {
    //纬度
    double lat = 1;
    //经度
    double lon = 2;
    //坐标类型，1：GPS/定位，2：IP地址；
    sint32 type = 3;
    //国家，例如：CN
    optional string country = 4;
    //省份
    optional string province = 5;
    //城市
    optional string city = 6;
  }
  message Network {
    //网络类型：0：未知，1：Wifi，2：2G，3：3G，4：4G，5：5G；
    sint32 conType = 1;
    //网络运营商，1：中国移动，2：中国电信，3：中国联通；
    sint32 carrier = 2;
    //IMSI(SIM 卡串号)，获取不到完整的值，请获取前五位，IMSI前五位是 mcc + mnc（不需要权限获取）
    optional string imsi = 3;
    //移动网络国家代码（中国移动为460）
    optional string mcc = 4;
    //移动网络号码（中国移动为00，中国联通为01）
    optional string mnc = 5;
    //MAC 地址，例如：66:61:d5:44:56:e6
    optional string mac = 6;
    //MAC MD5值，无法获取MAC地址时，可以传MD5值；
    optional string macMd5 = 7;
    //无线网SSID名称
    optional string ssid = 8;
    //wifiMac
    optional string wifiMac = 9;
  }
  message CaidList {
    //caid ios14替代idfa，例如：2a30adf7d1641daa3a940cc364b84f7d
    optional string id = 1;
    // caid 版本，iOS14 以上必填，例如：20230506
    optional string ver = 2;
  }
}
// 响应体
message BidResponse {
  //媒体请求时的唯一ID
  string id = 1;
  //响应码，200：有广告填充，404：无广告填充
  sint32 code = 2;
  //响应说明
  string msg = 3;
  //广告内容信息，无广告返回时此对象为空
  optional Bid bid = 4;
  message Bid {
    //本次竞价唯一ID(竞价模式)
    optional string bidId = 1;
    //广告位ID
    string tagId = 2;
    //广告标题
    optional string title = 3;
    //广告描述
    optional string desc = 4;
    //广告图标icon链接
    string iconUrl = 5;
    //广告图片链接，单个广告可能多个图片地址
    repeated string imgUrls = 6;
    //广告宽度，可能为空
    optional sint32 w = 7;
    //广告高度，可能为空
    optional sint32 h = 8;
    //落地页链接，若 ciType=3 时，该字段值可能为下载地址，与 downloadUrl 相同；
    string landingUrl = 9;
    //deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
    optional string deeplink = 10;
    //APP应用下载链接
    optional string downloadUrl = 11;
    //竞价广告出价，单位: 分/cpm(竞价模式)
    optional sint32 bidFloor = 12;
    //竞价成功上报地址，可能有多条(竞价模式)
    repeated string winUrls = 13;
    //竞价失败上报地址，可能有多条(竞价模式)
    repeated string loseUrls = 14;
    //广告的类型，1：文字，2：静态图片，3：图文，4：视频，5：激励视频，6：gif；
    optional sint32 cType = 15;
    //广告的交互类型，1：浏览器打开，2：应用内打开，3:直接下载应用，4：访问后下载；
    optional sint32 ciType = 16;
    //APP应用下载信息
    optional App app = 17;
    //视频广告素材
    optional Video video = 18;
    //Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
    repeated Tracker trackers = 19;
    //app 端 iOS DP调起链接，优先级高于deeplink；
    optional string universalLink = 20;
    //汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
    repeated string clickAreaReportUrls = 21;
    message App {
      //应用名称
      string name = 1;
      //应用包名      string bundle = 2;
      //应用包大小，单位：KB
      optional sint32 size = 3;
      //应用版本号
      optional string ver = 4;
      //隐私说明链接
      optional string privacyUrl = 5;
      //应用权限列表
      optional string permContent = 6;
      //应用开发者
      optional string developer = 7;
    }
    message Video {
      //视频地址URL
      string url = 1;
      //视频时长，单位：秒
      sint32 duration = 2;
      //视频体积，单位：byte
      optional sint64 size = 3;
      //视频宽度
      optional sint32 vw = 4;
      //视频高度
      optional sint32 vh = 5;
      //视频封面图链接
      repeated string coverImgUrl = 6;
      //视频播放过程中或完成后显示的按钮文章
      optional string buttonText = 7;      //视频播放完成展示图片链接，endImgUrl与endHtml，选择其中一个有值的进行展示；
      optional string endImgUrl = 8;
      //视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
      optional string endHtml = 9;
      //播放多少秒才可以跳过，有值且大于0才处理
      optional sint32 skipSec = 10;
      //视频播放完成是否自动跳 landingUrl，默认否
      optional bool comLanding = 11;
      //视频播放过程中是否可以点击跳转 landingUrl，默认否
      optional bool proLanding = 12;
      //是否需要预先加载广告视频，默认否
      optional bool prefetch = 13;
    }
    message Tracker {
      //监测上报类型, 参考 Tracker 对象 type 对应关系表
      sint32 type = 1;      //监测上报url列表
      repeated string urls = 2;
    }
  }
}